python.exe : import time: self [us] | cumulative | imported package
At line:1 char:6
+ cls; python.exe -X importtime -m .\dags\data_pipeline\utility_code.py ...
+      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : NotSpecified: (import time: se...mported package:String) [], RemoteException
    + FullyQualifiedErrorId : NativeCommandError
 
import time:       315 |        315 | winreg
import time:       619 |        619 |   _io
import time:        76 |         76 |   marshal
import time:       257 |        257 |   nt
import time:       577 |       1528 | _frozen_importlib_external
import time:       841 |        841 |   time
import time:       155 |        995 | zipimport
import time:        68 |         68 |     _codecs
import time:       464 |        532 |   codecs
import time:      2028 |       2028 |   encodings.aliases
import time:      2922 |       5481 | encodings
import time:      2612 |       2612 | encodings.utf_8
import time:      1859 |       1859 | encodings.cp1252
import time:       146 |        146 | _signal
import time:        87 |         87 |     _abc
import time:       307 |        394 |   abc
import time:       496 |        890 | io
import time:       246 |        246 |       _stat
import time:       210 |        455 |     stat
import time:      1857 |       1857 |     _collections_abc
import time:        96 |         96 |       genericpath
import time:       243 |        243 |       _winapi
import time:       213 |        551 |     ntpath
import time:       674 |       3536 |   os
import time:       186 |        186 |   _sitebuiltins
import time:      1476 |       1476 |   encodings.utf_8_sig
import time:      5617 |       5617 |   _virtualenv
import time:      1489 |       1489 |   _distutils_hack
import time:       931 |        931 |     pywin32_system32
import time:      2054 |       2985 |   pywin32_bootstrap
import time:       702 |        702 |   sitecustomize
import time:     14563 |      30550 | site
import time:      1458 |       1458 |       warnings
import time:      2169 |       3627 |     importlib
import time:       170 |       3797 |   importlib.machinery
import time:      1221 |       1221 |     importlib._abc
import time:      1235 |       1235 |     types
import time:       244 |       2698 |   importlib.util
import time:      1627 |       8121 | runpy
C:\Users\<USER>\PycharmProjects\airflow\.venv\Scripts\python.exe: Relative module names not supported
